"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createEditor = void 0;
const https_1 = require("firebase-functions/v2/https");
const admin = __importStar(require("firebase-admin"));
// Initialize the Firebase Admin SDK, which gives this code admin-level access
// to your Firebase project.
admin.initializeApp();
/**
 * Creates a new editor user account and sets a custom claim for their role.
 * This is an "HTTPS Callable function", which means it can be securely called
 * directly from your app.
 */
exports.createEditor = (0, https_1.onCall)(async (request) => {
    var _a;
    // 1. Authentication Check: Ensure the user calling this function is an admin.
    // We check the custom claim 'admin' on the calling user's token.
    // Fixed: Check if auth exists and access token properly
    if (!request.auth || !((_a = request.auth.token) === null || _a === void 0 ? void 0 : _a.admin)) {
        // If they are not an admin, throw an error.
        throw new https_1.HttpsError("permission-denied", "Only administrators can create new editors.");
    }
    // 2. Data Validation: Ensure the function was called with an email and password.
    const email = request.data.email;
    const password = request.data.password;
    if (!email || !password) {
        throw new https_1.HttpsError("invalid-argument", "The function must be called with a valid email and password.");
    }
    try {
        // 3. Create the new user account using the Firebase Admin SDK.
        const userRecord = await admin.auth().createUser({
            email: email,
            password: password,
        });
        // 4. Set a custom claim to identify this new user as an 'editor'.
        // This is crucial for securing your database and app features later.
        await admin.auth().setCustomUserClaims(userRecord.uid, { editor: true });
        // 5. Return a success message to the app.
        return {
            result: `Successfully created editor account for ${email}.`,
        };
    }
    catch (error) {
        console.error("Error creating new user:", error);
        // If an error occurs, send it back to the app so the user can see it.
        throw new https_1.HttpsError("internal", "An error occurred while creating the user.");
    }
});
//# sourceMappingURL=index.js.map