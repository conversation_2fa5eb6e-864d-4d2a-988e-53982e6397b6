// This script sets the 'admin' custom claim and then verifies it was set.
const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

// Initialize the Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Get the UID from the command line argument.
const uid = process.argv[2];

if (!uid) {
  console.error('Error: Please provide a UID as a command line argument.');
  console.log('Usage: node setAdmin.js <user_uid>');
  process.exit(1);
}

// Set the custom claim 'admin' to true for the specified user.
admin.auth().setCustomUserClaims(uid, { admin: true })
  .then(() => {
    console.log(`Attempted to set admin claim for user ${uid}.`);
    console.log('Verifying claim...');
    
    // --- VERIFICATION STEP ---
    // Immediately fetch the user record again to check the new claims.
    return admin.auth().getUser(uid);
  })
  .then((userRecord) => {
    // Check if the custom claims object and the admin property are correct.
    if (userRecord.customClaims && userRecord.customClaims.admin === true) {
      console.log('\nSUCCESS! Admin privileges have been successfully set and verified.');
      console.log('Custom claims:', userRecord.customClaims);
    } else {
      console.error('\nVERIFICATION FAILED: The admin claim was not set correctly.');
      console.log('Current claims:', userRecord.customClaims);
    }
    process.exit(0);
  })
  .catch((error) => {
    console.error('\nERROR: An error occurred during the process.');
    console.error(error);
    process.exit(1);
  });
