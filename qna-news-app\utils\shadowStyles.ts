import { Platform } from 'react-native';

export const createShadowStyle = (
  shadowColor: string = '#000',
  shadowOffset: { width: number; height: number } = { width: 0, height: 2 },
  shadowOpacity: number = 0.1,
  shadowRadius: number = 4,
  elevation: number = 4
) => {
  if (Platform.OS === 'web') {
    // For web, use a simple border instead of shadow to avoid React Native Web warnings
    return {
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.1)',
    };
  } else if (Platform.OS === 'android') {
    return {
      elevation,
    };
  } else {
    // iOS - only apply shadow properties on native iOS, not web
    return {
      shadowColor,
      shadowOffset,
      shadowOpacity,
      shadowRadius,
    };
  }
};

// Alternative function that completely avoids shadow properties for web compatibility
export const createWebSafeShadowStyle = (
  shadowColor: string = '#000',
  shadowOffset: { width: number; height: number } = { width: 0, height: 2 },
  shadowOpacity: number = 0.1,
  shadowRadius: number = 4,
  elevation: number = 4
) => {
  // Completely avoid shadow properties to prevent React Native Web warnings
  if (Platform.OS === 'android') {
    return {
      elevation,
      // Also add a subtle border for consistency
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.05)',
    };
  } else {
    // For web and iOS, use only border-based styling
    return {
      borderWidth: 1,
      borderColor: 'rgba(0, 0, 0, 0.1)',
    };
  }
};

// Completely web-safe function that never uses shadow properties
export const createBorderOnlyShadowStyle = (
  opacity: number = 0.1
) => {
  return {
    borderWidth: 1,
    borderColor: `rgba(0, 0, 0, ${opacity})`,
  };
};
