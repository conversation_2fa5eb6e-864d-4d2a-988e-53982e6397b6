import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  SafeAreaView,
  StatusBar,
  Platform,
  FlatList,
  Text,
  UIManager,
  LayoutAnimation,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator
} from 'react-native';

import { db } from '../../firebaseConfig';
import { doc, getDocs, collection, setDoc, getDoc } from 'firebase/firestore';

import ArticleCard from '../../components/ArticleCard';
import AIModal from '../../components/AIModal';
import SkeletonLoader from '../../components/SkeletonLoader';
import { useAuth } from '../../context/AuthContext';

const NEWS_API_KEY = process.env.EXPO_PUBLIC_NEWS_API_KEY;
const GEMINI_API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY;

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

type Article = {
  source: { id: string | null; name: string; };
  author: string | null; title: string; description: string; url: string;
  urlToImage: string | null; publishedAt: string; content: string | null;
  sentiment?: 'Positive' | 'Negative' | 'Neutral';
};

const tags = ['Qatar', 'Business', 'Technology', 'Sports', 'Health', 'Science', 'Entertainment'];

export default function HomeScreen() {
  const { isEditor } = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const [articles, setArticles] = useState<Article[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
  const [modalMode, setModalMode] = useState<'summary' | 'article'>('summary');
  const [modalContent, setModalContent] = useState<string>('');
  const [isModalLoading, setIsModalLoading] = useState<boolean>(false);
  const [isSummarizing, setIsSummarizing] = useState<boolean>(false);
  const [summary, setSummary] = useState<string>('');
  const [summaryError, setSummaryError] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('Qatar');
  
  // --- NEW STATE for background processing ---
  const [articlesToProcess, setArticlesToProcess] = useState<Article[]>([]);

  const callGeminiAPI = async (prompt: string) => {
    if (!GEMINI_API_KEY) {
      throw new Error("Gemini API Key is missing.");
    }
    
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ]
      })
    });

    const data = await response.json();
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text;
    }
    throw new Error("Failed to generate content");
  };

  const getArticleSentiment = async (article: Article): Promise<'Positive' | 'Negative' | 'Neutral'> => {
    try {
      const prompt = `Analyze the sentiment of the following news headline and description. Respond with only a single word: Positive, Negative, or Neutral.\n\nHeadline: ${article.title}\nDescription: ${article.description}`;
      const result = await callGeminiAPI(prompt);
      const sentiment = result.trim();

      if (['Positive', 'Negative', 'Neutral'].includes(sentiment)) {
        return sentiment as 'Positive' | 'Negative' | 'Neutral';
      }
      return 'Neutral';
    } catch (error) {
      console.error("Sentiment analysis failed for article:", article.title, error);
      return 'Neutral';
    }
  };

  const saveArticlesToFirestore = async (articlesToSave: Article[]) => {
    for (const article of articlesToSave) {
      if (article.url) {
        const docId = article.url.replace(/\//g, "_");
        const articleDocRef = doc(db, "articles", docId);

        const docSnap = await getDoc(articleDocRef);
        if (!docSnap.exists() || !docSnap.data().sentiment) {
          const sentiment = await getArticleSentiment(article);
          await setDoc(articleDocRef, { ...article, sentiment }, { merge: true });
        }
      }
    }
  };

  const handleGenerateSummary = async () => {
    if (!GEMINI_API_KEY) {
      setSummaryError("Gemini API Key is missing.");
      return;
    }
    
    setIsSummarizing(true);
    setSummary('');
    setSummaryError('');
    
    try {
      const articlesText = articles.map(article => 
        `Title: ${article.title}\nDescription: ${article.description}\n`
      ).join('\n');
      
      const prompt = `Please provide a comprehensive daily news summary in English based on the following articles. Make it professional and suitable for a news agency:\n\n${articlesText}`;
      
      const result = await callGeminiAPI(prompt);
      setSummary(result);
    } catch (error) {
      setSummaryError("Failed to generate summary. Please try again.");
      console.error("Summary generation failed:", error);
    } finally {
      setIsSummarizing(false);
    }
  };

  const handleSummarizeArticle = async (article: Article) => {
    setSelectedArticle(article);
    setModalMode('summary');
    setModalVisible(true);
    setIsModalLoading(true);
    setModalContent('');
    
    try {
      const prompt = `Please provide a comprehensive summary of the following news article in English:\n\nTitle: ${article.title}\nDescription: ${article.description}\nContent: ${article.content || 'No additional content available.'}`;
      const result = await callGeminiAPI(prompt);
      setModalContent(result);
    } catch (error) {
      setModalContent("Failed to generate summary. Please try again.");
      console.error("Article summarization failed:", error);
    } finally {
      setIsModalLoading(false);
    }
  };

  const handleWriteArticle = async (article: Article) => {
    setSelectedArticle(article);
    setModalMode('article');
    setModalVisible(true);
    setIsModalLoading(true);
    setModalContent('');
    
    try {
      const prompt = `Based on the following news information, write a comprehensive news article in English suitable for Qatar News Agency. Make it professional, detailed, and well-structured:\n\nTitle: ${article.title}\nDescription: ${article.description}\nContent: ${article.content || 'No additional content available.'}`;
      const result = await callGeminiAPI(prompt);
      setModalContent(result);
    } catch (error) {
      setModalContent("Failed to generate article. Please try again.");
      console.error("Article writing failed:", error);
    } finally {
      setIsModalLoading(false);
    }
  };

  // --- MODIFIED useEffect for fetching and displaying news ---
  useEffect(() => {
    const fetchNewsForDisplay = async () => {
      if (!NEWS_API_KEY) {
        setError("News API Key is missing.");
        setLoading(false);
        return;
      }
      setLoading(true);
      setArticles([]);
      try {
        const query = selectedTag.toLowerCase();
        const response = await fetch(`https://newsapi.org/v2/everything?q=${query}&sortBy=publishedAt&language=en&apiKey=${NEWS_API_KEY}`);
        const data = await response.json();

        if (data.status === "ok") {
          LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          // 1. Show articles to the user immediately
          setArticles(data.articles);
          setError(null);
          
          // 2. Queue up the articles for background processing
          setArticlesToProcess(data.articles);
        } else {
          setError(data.message || "An error occurred while fetching news.");
        }
      } catch (e) {
        setError("Failed to connect to the news service.");
      } finally {
        // 3. Hide the loader as soon as the initial list is displayed
        setLoading(false);
      }
    };
    fetchNewsForDisplay();
  }, [selectedTag]);

  // --- NEW useEffect for background processing ---
  useEffect(() => {
    const processAndSave = async () => {
      if (articlesToProcess.length > 0) {
        // This runs silently in the background
        await saveArticlesToFirestore(articlesToProcess);
        setArticlesToProcess([]); // Clear the queue after processing
      }
    };

    processAndSave();
  }, [articlesToProcess]);

  return (
    <SafeAreaView style={styles.container}>
      <AIModal 
        visible={modalVisible}
        mode={modalMode}
        title={selectedArticle?.title || ''}
        content={modalContent}
        isLoading={isModalLoading}
        onClose={() => setModalVisible(false)}
      />

      <View style={styles.header}>
        <Text style={styles.title}>وكالة الأنباء القطرية</Text>
        <Text style={styles.subtitle}>Qatar News Agency</Text>
      </View>
      
      {isEditor && (
        <View style={styles.summaryContainer}>
          <TouchableOpacity style={styles.summarizeButton} onPress={handleGenerateSummary} disabled={isSummarizing}>
            <Text style={styles.buttonText}>{isSummarizing ? "Generating..." : "Generate Daily Summary"}</Text>
          </TouchableOpacity>
          {isSummarizing && <ActivityIndicator style={{ marginTop: 10 }} color="#8A0C2E" />}
          {summary && (
            <ScrollView style={styles.summaryBox}>
              <Text style={styles.summaryTitle}>AI-Generated Daily Brief</Text>
              <Text style={styles.summaryBodyText}>{summary}</Text>
            </ScrollView>
          )}
          {summaryError && <Text style={styles.errorText}>{summaryError}</Text>}
        </View>
      )}

      <View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tagsContainer}>
          {tags.map(tag => (
            <TouchableOpacity 
              key={tag} 
              style={[styles.tag, selectedTag === tag && styles.tagSelected]} 
              onPress={() => setSelectedTag(tag)}
            >
              <Text style={[styles.tagText, selectedTag === tag && styles.tagTextSelected]}>{tag}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.listHeader}>
        <Text style={styles.listHeaderText}>Latest News</Text>
      </View>

      {loading ? (
        <SkeletonLoader />
      ) : error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : (
        <FlatList
          data={articles}
          renderItem={({ item }) => (
            <ArticleCard 
              article={item}
              onSummarize={handleSummarizeArticle}
              onWriteArticle={handleWriteArticle}
              isEditor={isEditor}
            />
          )}
          keyExtractor={(item, index) => item.url + index}
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#F8F9FA', paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0 },
  header: { paddingVertical: 15, alignItems: 'center', borderBottomWidth: 1, borderBottomColor: '#EAEAEA', backgroundColor: '#FFFFFF' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#8A0C2E' },
  subtitle: { fontSize: 18, color: '#555' },
  summaryContainer: { padding: 16, borderBottomWidth: 1, borderBottomColor: '#EAEAEA', backgroundColor: '#FFFFFF' },
  summarizeButton: { backgroundColor: '#8A0C2E', paddingVertical: 12, borderRadius: 8, alignItems: 'center', justifyContent: 'center' },
  buttonText: { color: '#FFFFFF', fontSize: 16, fontWeight: '600' },
  summaryBox: { marginTop: 15, padding: 12, backgroundColor: '#F8F9FA', borderRadius: 8, maxHeight: 150, borderWidth: 1, borderColor: '#EAEAEA' },
  summaryTitle: { fontSize: 16, fontWeight: '700', marginBottom: 8, color: '#1C1C1E' },
  summaryBodyText: { fontSize: 15, lineHeight: 22, color: '#495057' },
  tagsContainer: { paddingVertical: 10, paddingHorizontal: 16 },
  tag: { paddingVertical: 8, paddingHorizontal: 16, borderRadius: 20, backgroundColor: '#E9ECEF', marginRight: 10 },
  tagSelected: { backgroundColor: '#8A0C2E' },
  tagText: { color: '#495057', fontWeight: '600' },
  tagTextSelected: { color: '#FFFFFF' },
  listHeader: { paddingHorizontal: 16, paddingTop: 10, paddingBottom: 8 },
  listHeaderText: { fontSize: 22, fontWeight: '700', color: '#1C1C1E' },
  errorText: { textAlign: 'center', color: 'red', fontSize: 16, padding: 20 },
});