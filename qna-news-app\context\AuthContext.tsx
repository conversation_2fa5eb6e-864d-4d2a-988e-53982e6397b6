import React, { createContext, useState, useEffect, useContext } from 'react';
import { getAuth, onIdTokenChanged, User } from 'firebase/auth';
import { app } from '../firebaseConfig';

const auth = getAuth(app);

// The context will now track both admin and editor status
interface AuthContextType {
  user: User | null;
  isAdmin: boolean;
  isEditor: boolean; // <-- NEW: To track editor status
  loading: boolean;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  isAdmin: false,
  isEditor: false, // Default to false
  loading: true,
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isEditor, setIsEditor] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onIdTokenChanged(auth, async (user) => {
      setUser(user);
      if (user) {
        const tokenResult = await user.getIdTokenResult(true);
        console.log("User Token Claims:", tokenResult.claims);

        const adminStatus = tokenResult.claims.admin === true;
        const editorStatus = tokenResult.claims.editor === true;

        setIsAdmin(adminStatus);
        // An admin is also considered an editor, so they get all editor permissions.
        setIsEditor(adminStatus || editorStatus);
        
      } else {
        // If no user is logged in, they have no special roles.
        setIsAdmin(false);
        setIsEditor(false);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // We don't need the forceRefresh function anymore, so it has been removed.
  return (
    <AuthContext.Provider value={{ user, isAdmin, isEditor, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  return useContext(AuthContext);
};
