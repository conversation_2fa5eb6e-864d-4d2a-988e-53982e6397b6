import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

// This component will be the placeholder for a single article card while loading.
const SkeletonPlaceholder = () => {
  // We'll use the Animated API to create a shimmering effect.
  const animatedValue = new Animated.Value(0);

  React.useEffect(() => {
    Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1200, // Duration of one shimmer cycle
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  }, []);

  // Interpolate the animated value to move a gradient across the placeholder.
  const translateX = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [-200, 200],
  });

  return (
    <View style={styles.placeholderItem}>
      <View style={styles.skeletonTextLarge} />
      <View style={styles.skeletonTextSmall} />
      <View style={styles.skeletonButton} />
      {/* The Shimmer Effect View */}
      <Animated.View 
        style={[
          styles.shimmer,
          { transform: [{ translateX }] }
        ]}
      />
    </View>
  );
};

// This component renders a list of the placeholders.
const SkeletonLoader = () => {
  return (
    <View style={{ flex: 1 }}>
      {/* Create an array of 5 placeholders to fill the screen */}
      {[...Array(5)].map((_, index) => (
        <SkeletonPlaceholder key={index} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  placeholderItem: {
    backgroundColor: '#E1E9EE',
    padding: 15,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden', // Important for the shimmer effect
  },
  skeletonTextLarge: {
    width: '90%',
    height: 20,
    backgroundColor: '#CED9E0',
    borderRadius: 4,
    marginBottom: 10,
  },
  skeletonTextSmall: {
    width: '60%',
    height: 16,
    backgroundColor: '#CED9E0',
    borderRadius: 4,
    marginBottom: 15,
  },
  skeletonButton: {
    width: '40%',
    height: 24,
    backgroundColor: '#CED9E0',
    borderRadius: 4,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: '100%',
  },
});

export default SkeletonLoader;
