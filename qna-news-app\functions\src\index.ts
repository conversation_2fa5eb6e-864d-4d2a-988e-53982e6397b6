import {onCall, HttpsError} from "firebase-functions/v2/https";
import * as admin from "firebase-admin";

// Initialize the Firebase Admin SDK, which gives this code admin-level access
// to your Firebase project.
admin.initializeApp();

/**
 * Creates a new editor user account and sets a custom claim for their role.
 * This is an "HTTPS Callable function", which means it can be securely called
 * directly from your app.
 */
export const createEditor = onCall(async (request) => {
  // 1. Authentication Check: Ensure the user calling this function is an admin.
  // We check the custom claim 'admin' on the calling user's token.
  // Fixed: Check if auth exists and access token properly
  if (!request.auth || !(request.auth.token as any)?.admin) {
    // If they are not an admin, throw an error.
    throw new HttpsError(
      "permission-denied",
      "Only administrators can create new editors.",
    );
  }

  // 2. Data Validation: Ensure the function was called with an email and password.
  const email = request.data.email as string;
  const password = request.data.password as string;

  if (!email || !password) {
    throw new HttpsError(
      "invalid-argument",
      "The function must be called with a valid email and password.",
    );
  }

  try {
    // 3. Create the new user account using the Firebase Admin SDK.
    const userRecord = await admin.auth().createUser({
      email: email,
      password: password,
    });

    // 4. Set a custom claim to identify this new user as an 'editor'.
    // This is crucial for securing your database and app features later.
    await admin.auth().setCustomUserClaims(userRecord.uid, { editor: true });

    // 5. Return a success message to the app.
    return {
      result: `Successfully created editor account for ${email}.`,
    };
  } catch (error) {
    console.error("Error creating new user:", error);
    // If an error occurs, send it back to the app so the user can see it.
    throw new HttpsError(
      "internal",
      "An error occurred while creating the user.",
    );
  }
});
