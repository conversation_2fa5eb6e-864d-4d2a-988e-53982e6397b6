import React, { useState } from 'react';
import { StyleSheet, View, Text, TextInput, ScrollView, TouchableOpacity, Image, ActivityIndicator, SafeAreaView } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { callGeminiAPI } from '../../lib/gemini'; // Import from our new helper file

// A simple Icon component placeholder
const AIToolIcon = () => <Text style={styles.aiIcon}>✨</Text>;

export default function EditScreen() {
  const params = useLocalSearchParams();
  const article = JSON.parse(params.article as string);

  const [editedText, setEditedText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleAIToolSelect = async (tool: string) => {
    let prompt = '';
    const sourceText = editedText.trim() || article.description;

    switch (tool) {
      case 'formal':
        prompt = `Rewrite the following text in a formal and professional tone:\n\n${sourceText}`;
        break;
      case 'social':
        prompt = `Rewrite the following text as a social media post, including relevant hashtags:\n\n${sourceText}`;
        break;
      case 'summarize_input':
        if (!editedText.trim()) {
          alert("Please write something in the editor before summarizing.");
          return;
        }
        prompt = `Provide a concise summary of the following text:\n\n${editedText}`;
        break;
      case 'summarize_news':
        prompt = `Provide a concise summary of the following news article:\n\nTitle: ${article.title}\nDescription: ${article.description}`;
        break;
      case 'expand':
        if (!editedText.trim()) {
          alert("Please write something in the editor before expanding.");
          return;
        }
        prompt = `Expand on the following text, making it longer and more detailed while preserving the core message:\n\n${editedText}`;
        break;
      default:
        return;
    }

    setIsLoading(true);
    try {
      const result = await callGeminiAPI(prompt);
      setEditedText(result);
    } catch (error) {
      console.error("AI Tool Error:", error);
      alert("An error occurred while using the AI tool. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const aiTools = [
    { key: 'formal', label: 'Make Formal' },
    { key: 'social', label: 'Social Media Post' },
    { key: 'summarize_input', label: 'Summarize My Text' },
    { key: 'summarize_news', label: 'Summarize News' },
    { key: 'expand', label: 'Make Longer' },
  ];

  return (
    <SafeAreaView style={styles.safeArea}>
      <Stack.Screen options={{ title: 'Edit with AI', headerTitleStyle: { color: '#000' }, headerTintColor: '#8A0C2E' }} />
      <ScrollView style={styles.container} keyboardShouldPersistTaps="handled">
        <View style={styles.articleContainer}>
          <Image source={{ uri: article.urlToImage }} style={styles.image} />
          <Text style={styles.title}>{article.title}</Text>
          <Text style={styles.description}>{article.description}</Text>
          <Text style={styles.source}>Source: {article.source.name}</Text>
        </View>

        <View style={styles.editorContainer}>
          <Text style={styles.editorTitle}>Write Your Article</Text>
          <View style={styles.textInputWrapper}>
            <TextInput
              style={styles.textInput}
              multiline
              placeholder="Start writing or use AI tools to generate content..."
              value={editedText}
              onChangeText={setEditedText}
            />
          </View>
        </View>

        <View style={styles.aiToolsContainer}>
           <View style={styles.aiHeader}>
             <AIToolIcon />
             <Text style={styles.aiToolsTitle}>AI Tools</Text>
           </View>
          {isLoading ? (
             <ActivityIndicator size="large" color="#8A0C2E" style={{ marginVertical: 20 }}/>
          ) : (
            <View style={styles.toolsGrid}>
              {aiTools.map((tool) => (
                <TouchableOpacity key={tool.key} style={styles.toolButton} onPress={() => handleAIToolSelect(tool.key)}>
                  <Text style={styles.toolButtonText}>{tool.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: '#FFFFFF' },
  container: { flex: 1, backgroundColor: '#F8F9FA' },
  articleContainer: { padding: 16, backgroundColor: '#FFFFFF', borderBottomWidth: 1, borderBottomColor: '#EAEAEA' },
  image: { width: '100%', height: 200, borderRadius: 8, marginBottom: 12 },
  title: { fontSize: 22, fontWeight: 'bold', marginBottom: 8, color: '#1C1C1E' },
  description: { fontSize: 16, lineHeight: 24, color: '#495057', marginBottom: 8 },
  source: { fontSize: 14, color: '#6C757D', fontStyle: 'italic' },
  editorContainer: { padding: 16 },
  editorTitle: { fontSize: 20, fontWeight: '700', marginBottom: 12, color: '#1C1C1E' },
  textInputWrapper: { borderWidth: 1, borderColor: '#CED4DA', borderRadius: 8, backgroundColor: '#FFFFFF' },
  textInput: { height: 200, padding: 12, fontSize: 16, textAlignVertical: 'top' },
  aiToolsContainer: { padding: 16 },
  aiHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  aiIcon: { fontSize: 24, marginRight: 8 },
  aiToolsTitle: { fontSize: 20, fontWeight: '700', color: '#1C1C1E' },
  toolsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' },
  toolButton: { backgroundColor: '#8A0C2E', paddingVertical: 12, paddingHorizontal: 10, borderRadius: 8, marginBottom: 10, width: '48%', alignItems: 'center' },
  toolButtonText: { color: '#FFFFFF', fontSize: 14, fontWeight: '600', textAlign: 'center' },
});