import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { Link } from 'expo-router';

// This is the full, correct type for an Article object
type Article = {
  source: { id: string | null; name: string; };
  author: string | null;
  title: string;
  description: string;
  url: string;
  urlToImage: string | null;
  publishedAt: string;
  content: string | null;
};

interface ArticleCardProps {
  article: Article;
  isEditor: boolean;
  // --- FIX: Allow async functions (which return a Promise) ---
  onSummarize: (article: Article) => Promise<void>;
  onWriteArticle: (article: Article) => Promise<void>;
}

const ArticleCard: React.FC<ArticleCardProps> = ({ article, isEditor, onSummarize, onWriteArticle }) => {
  return (
    <View style={styles.cardContainer}>
      <TouchableOpacity onPress={() => Linking.openURL(article.url)}>
        <Text style={styles.sourceText}>{article.source.name.toUpperCase()}</Text>
        <Text style={styles.titleText}>{article.title}</Text>
      </TouchableOpacity>
      
      {/* --- Only show buttons if the user is an editor --- */}
      {isEditor && (
        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton} onPress={() => onSummarize(article)}>
            <Text style={styles.actionButtonText}>Summarize</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, styles.writeButton]} onPress={() => onWriteArticle(article)}>
            <Text style={[styles.actionButtonText, styles.writeButtonText]}>Write Article</Text>
          </TouchableOpacity>
          <Link href={{ pathname: "/(editor)/edit", params: { article: JSON.stringify(article) } }} asChild>
            <TouchableOpacity style={[styles.actionButton, styles.aiButton]}>
              <Text style={[styles.actionButtonText, styles.aiButtonText]}>Edit with AI ✨</Text>
            </TouchableOpacity>
          </Link>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: { backgroundColor: '#FFFFFF', borderRadius: 12, padding: 16, marginHorizontal: 16, marginVertical: 8, elevation: 4, shadowColor: '#000000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, borderWidth: 1, borderColor: '#EAEAEA' },
  sourceText: { fontSize: 12, fontWeight: '700', color: '#6C757D', marginBottom: 4, letterSpacing: 0.5 },
  titleText: { fontSize: 17, fontWeight: '600', color: '#1C1C1E', lineHeight: 24, marginBottom: 16 },
  actionsContainer: { flexDirection: 'row', borderTopWidth: 1, borderTopColor: '#EAEAEA', paddingTop: 12 },
  actionButton: { backgroundColor: '#F8F9FA', paddingVertical: 10, paddingHorizontal: 14, borderRadius: 8, marginRight: 10, borderWidth: 1, borderColor: '#DEE2E6' },
  writeButton: { backgroundColor: '#8A0C2E', borderColor: '#8A0C2E' },
  actionButtonText: { color: '#495057', fontSize: 13, fontWeight: '600' },
  writeButtonText: { color: '#FFFFFF' },
  aiButton: { backgroundColor: '#8A0C2E', borderColor: '#8A0C2E' },
  aiButtonText: { color: '#FFFFFF' },
});

export default ArticleCard;
