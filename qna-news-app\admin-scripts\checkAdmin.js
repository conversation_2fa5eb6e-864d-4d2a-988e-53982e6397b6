// This script checks the custom claims for a given user UID.
const admin = require('firebase-admin');

// It uses the same service account key as the setAdmin script.
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// Get the UID from the command line argument.
const uid = process.argv[2];

if (!uid) {
  console.error('Error: Please provide a UID as a command line argument.');
  console.log('Usage: node checkAdmin.js <user_uid>');
  process.exit(1);
}

// Get the user record from Firebase Authentication.
admin.auth().getUser(uid)
  .then((userRecord) => {
    console.log(`Successfully fetched user data for: ${userRecord.email}`);
    console.log('Custom claims for this user are:');
    // Print the custom claims object.
    console.log(userRecord.customClaims);

    if (userRecord.customClaims && userRecord.customClaims.admin === true) {
        console.log('\nSUCCESS: This user has admin privileges.');
    } else {
        console.log('\nFAILURE: This user does NOT have admin privileges set.');
    }
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error fetching user data:', error);
    process.exit(1);
  });
