import React from 'react';
import { Tabs, useRouter } from 'expo-router';
import { getAuth, signOut } from 'firebase/auth';
import { TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext'; // Import useAuth

// --- IMPORTANT: DEFINE THE ADMIN'S EMAIL ADDRESS ---
const ADMIN_EMAIL = "<EMAIL>"; // <--- REPLACE WITH YOUR EMAIL

export default function TabsLayout() {
  const { user } = useAuth(); // Get the current user
  const auth = getAuth();

  const handleLogout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error("Logout Error: ", error);
      Alert.alert("Logout Failed", "An error occurred while trying to log out.");
    }
  };

  return (
    <Tabs
      screenOptions={{
        headerStyle: { backgroundColor: '#FFFFFF' },
        headerTintColor: '#1C1C1E',
        headerTitleStyle: { fontWeight: 'bold' },
        tabBarActiveTintColor: '#8A0C2E',
        headerRight: () => (
          <TouchableOpacity onPress={handleLogout} style={{ marginRight: 15 }}>
            <Ionicons name="log-out-outline" size={28} color="#8A0C2E" />
          </TouchableOpacity>
        ),
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="home-outline" size={size} color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Explore',
          tabBarIcon: ({ color, size }) => (
            <Ionicons name="compass-outline" size={size} color={color} />
          ),
        }}
      />
      {/* --- CONDITIONALLY ADD THE ADMIN TAB --- */}
      {user?.email === ADMIN_EMAIL && (
        <Tabs.Screen
          name="admin"
          options={{
            title: 'Admin',
            tabBarIcon: ({ color, size }) => (
              <Ionicons name="shield-checkmark-outline" size={size} color={color} />
            ),
          }}
        />
      )}
    </Tabs>
  );
}
