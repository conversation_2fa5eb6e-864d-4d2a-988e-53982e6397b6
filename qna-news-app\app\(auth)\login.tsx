import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Alert, 
  ActivityIndicator, 
  Platform 
} from 'react-native';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

const LoginScreen = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const auth = getAuth();

  const handleLogin = async () => {
    if (loading || !email || !password) return;
    setLoading(true);
    try {
      await signInWithEmailAndPassword(auth, email, password);
      // On successful login, the root layout will handle the redirect.
    } catch (error: any) {
      Alert.alert("Login Failed", "Please check your email and password.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.loginBox}>
        <Text style={styles.title}>QNA Editor Portal</Text>
        <Text style={styles.subtitle}>Please sign in to continue</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          placeholderTextColor="#999"
        />
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          placeholderTextColor="#999"
        />
        <TouchableOpacity style={styles.button} onPress={handleLogin} disabled={loading}>
          {loading ? <ActivityIndicator color="#FFFFFF" /> : <Text style={styles.buttonText}>Login</Text>}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    container: { 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center',
        padding: 20, 
        backgroundColor: '#F0F2F5' 
    },
    loginBox: {
        width: '100%',
        maxWidth: 400,
        padding: 30,
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        ...Platform.select({
            ios: {
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.1,
                shadowRadius: 12,
            },
            android: {
                elevation: 5,
            },
            web: {
                boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
            }
        }),
    },
    title: { 
        fontSize: 26, 
        fontWeight: 'bold', 
        color: '#8A0C2E', 
        textAlign: 'center', 
        marginBottom: 8 
    },
    subtitle: {
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
        marginBottom: 30,
    },
    input: { 
        height: 50, 
        borderColor: '#DEE2E6', 
        borderWidth: 1, 
        borderRadius: 8, 
        paddingHorizontal: 15, 
        marginBottom: 15, 
        backgroundColor: '#FFFFFF',
        fontSize: 16,
    },
    button: { 
        backgroundColor: '#8A0C2E', 
        padding: 15, 
        borderRadius: 8, 
        alignItems: 'center', 
        marginTop: 10,
    },
    buttonText: { 
        color: '#FFFFFF', 
        fontSize: 16, 
        fontWeight: 'bold' 
    },
});

export default LoginScreen;