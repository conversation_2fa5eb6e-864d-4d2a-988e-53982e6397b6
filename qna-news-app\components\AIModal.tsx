import React from 'react';
import { Modal, View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity, SafeAreaView } from 'react-native';

interface AIModalProps {
  visible: boolean;
  mode: 'summary' | 'article';
  title: string;
  content: string;
  isLoading: boolean;
  onClose: () => void;
}

const AIModal: React.FC<AIModalProps> = ({ visible, mode, title, content, isLoading, onClose }) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.modalBackdrop}>
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{mode === 'summary' ? 'AI Summary' : 'AI-Generated Article'}</Text>
          </View>
          <ScrollView style={styles.modalScrollView}>
            <Text style={styles.articleTitleText}>{title}</Text>
            {isLoading ? (
              <ActivityIndicator size="large" color="#8A0C2E" style={{marginTop: 20}} />
            ) : (
              <Text style={styles.modalBodyText}>{content}</Text>
            )}
          </ScrollView>
          <View style={styles.modalFooter}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
  },
  modalContainer: {
    flex: 1,
    margin: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EAEAEA',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1C1C1E',
    textAlign: 'center',
  },
  modalScrollView: {
    flex: 1,
    padding: 16,
  },
  articleTitleText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#8A0C2E',
    lineHeight: 25,
  },
  modalBodyText: {
    fontSize: 16,
    lineHeight: 24,
    color: '#495057',
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#EAEAEA',
  },
  closeButton: {
    backgroundColor: '#8A0C2E',
    padding: 14,
    borderRadius: 10,
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default AIModal;
