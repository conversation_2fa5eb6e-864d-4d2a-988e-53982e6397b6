import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useAuth } from '../../context/AuthContext';
import { getFunctions, httpsCallable } from 'firebase/functions';

const AdminScreen = () => {
  const { isAdmin } = useAuth();
  const [newEmail, setNewEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleCreateEditor = async () => {
    if (!newEmail || !newPassword) {
      Alert.alert("Missing Information", "Please provide both an email and a password.");
      return;
    }
    setLoading(true);

    try {
      const functions = getFunctions();
      const createEditorFunction = httpsCallable(functions, 'createEditor');
      await createEditorFunction({ email: newEmail, password: newPassword });

      // --- UPDATED SUCCESS MESSAGE ---
      Alert.alert("Success", "Created successfully");

      setNewEmail('');
      setNewPassword('');
    } catch (error: any) {
      console.error(error);
      Alert.alert("Error", error.message || "Could not create editor account.");
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return (
      <View style={styles.container}>
        <Text style={styles.deniedText}>Access Denied. This page is for administrators only.</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.adminBox}>
        <Text style={styles.title}>Admin Control Panel</Text>
        <Text style={styles.subtitle}>Create New Editor Account</Text>
        
        <TextInput
          style={styles.input}
          placeholder="New Editor's Email"
          value={newEmail}
          onChangeText={setNewEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          placeholderTextColor="#999"
        />
        <TextInput
          style={styles.input}
          placeholder="Temporary Password"
          value={newPassword}
          onChangeText={setNewPassword}
          secureTextEntry
          placeholderTextColor="#999"
        />
        <TouchableOpacity style={styles.button} onPress={handleCreateEditor} disabled={loading}>
          {loading ? <ActivityIndicator color="#FFFFFF" /> : <Text style={styles.buttonText}>Create Editor</Text>}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
    container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20, backgroundColor: '#F8F9FA' },
    adminBox: { width: '100%', maxWidth: 500, padding: 30, backgroundColor: '#FFFFFF', borderRadius: 12 },
    title: { fontSize: 24, fontWeight: 'bold', color: '#1C1C1E', textAlign: 'center', marginBottom: 8 },
    subtitle: { fontSize: 16, color: '#6C757D', textAlign: 'center', marginBottom: 30 },
    input: { height: 50, borderColor: '#DEE2E6', borderWidth: 1, borderRadius: 8, paddingHorizontal: 15, marginBottom: 15, backgroundColor: '#FFFFFF', fontSize: 16 },
    button: { backgroundColor: '#8A0C2E', padding: 15, borderRadius: 8, alignItems: 'center', marginTop: 10 },
    buttonText: { color: '#FFFFFF', fontSize: 16, fontWeight: 'bold' },
    deniedText: { fontSize: 18, color: 'red', textAlign: 'center' }
});

export default AdminScreen;
